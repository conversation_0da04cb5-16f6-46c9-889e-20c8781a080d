/// <reference types="vite/client" />

declare module 'react-toastify' {
  interface ToastFunction {
    (content: string, options?: {
      type?: 'info' | 'success' | 'warning' | 'error';
      autoClose?: number;
      position?: 'top-right' | 'top-center' | 'top-left' | 'bottom-right' | 'bottom-center' | 'bottom-left';
    }): void;
    success(content: string, options?: Omit<Parameters<ToastFunction>[1], 'type'>): void;
    error(content: string, options?: Omit<Parameters<ToastFunction>[1], 'type'>): void;
    info(content: string, options?: Omit<Parameters<ToastFunction>[1], 'type'>): void;
    warning(content: string, options?: Omit<Parameters<ToastFunction>[1], 'type'>): void;
  }

  export const toast: ToastFunction;
  
  export function ToastContainer(props: {
    position?: 'top-right' | 'top-center' | 'top-left' | 'bottom-right' | 'bottom-center' | 'bottom-left';
    autoClose?: number;
    hideProgressBar?: boolean;
    newestOnTop?: boolean;
    closeOnClick?: boolean;
    rtl?: boolean;
    pauseOnFocusLoss?: boolean;
    draggable?: boolean;
    pauseOnHover?: boolean;
    theme?: 'light' | 'dark' | 'colored';
  }): JSX.Element;
}

interface ImportMetaEnv {
  readonly VITE_API_URL: string
}

interface ImportMeta {
  readonly env: ImportMetaEnv
}

interface EurolandAppContext {
  command: (cmd: string) => {
    id: string; accessToken: string; isAuthenticated: boolean 
};
  on: (event: string, callback: () => void) => void;
  off: (event: string, callback: () => void) => void;
  registerCommandHandler: (cmd: string, handler: () => void) => void;
  emit: (event: string, data) => void;
  unregisterCommandHandler: (cmd: string, handler: () => void) => void;
}

interface Window {
  EurolandAppContext: EurolandAppContext;
  xprops?: {
    instrumentId?: string;
    layout?: {
      middle?: string;
    };
  };
  euroland?: {
    createComponent: (name: string, config: any) => void;
    components: {
      WatchlistAddInstrument: (props: {
        onChange: () => void;
        instrumentId: string;
      }) => {
        renderTo: (target: Window, selector: string) => void;
      };
    };
  };
}
