import { render } from "preact";
import { Provider } from "urql";
import { client } from "./services/graphql/client";
import styleString from "./index.css?raw";
import watchlistStyleString from "./components/Watchlist.css?raw";
import watchlistTabsStyleString from "./components/watchlist/WatchlistTabs.css?raw";
import EmptyWatchlistStateString from "./components/watchlist/EmptyWatchlistState.css?raw";
import InstrumentSearchStyleString from "./components/watchlist/InstrumentSearch.css?raw";
import InstrumentTableStyleString from "./components/watchlist/InstrumentTable.css?raw";
import AddInstrumentStyleString from "./components/AddInstrument.css?raw";
import { QueryClient, QueryClientProvider } from "@preact-signals/query";
import AddInstrumentWidget from "./components/AddInstrumentWidget.tsx";
import WatchlistWidget from "./components/WatchlistWidget.tsx";
import WatchlistModalStyleString from "./components/addInstrument/WatchlistModal.css?raw";
import RootIframeWidget from "./components/RootIframeWidget.tsx";

const searchParams = new URLSearchParams(window.location.search);
const searchString = searchParams.toString();
window.euroland?.createComponent("WatchlistAddInstrument", {
  tag: "watchlist-add-instrument",
  url:
    "/watchlist-add-instrument" +
    (searchString.length ? `?${searchString}` : ""),
  dimensions: {
    width: "500px",
    height: "600px",
  },
  template: {
    name: "modal",
    clickOverlayToClose: false,
    styles: {
      position: "fixed",
      bottom: "0px",
    },
  },
  props: {
    onChange: {
      type: "function",
      required: true,
    },
    instrumentId: {
      type: "string",
      required: true,
    },
  },
});

window.addEventListener("error", (e) => {
  if (e.message.includes("ResizeObserver loop")) {
    e.stopImmediatePropagation();
    return false;
  }
});

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 1000 * 60 * 5,
      cacheTime: 1000 * 60 * 10,
    },
  },
});
class EurolandWatchList extends HTMLElement {
  private container: HTMLDivElement | null = null;

  constructor() {
    super();

    this.attachShadow({ mode: "open" });

    const style = document.createElement("style");
    style.textContent = styleString;
    style.textContent += watchlistStyleString;
    style.textContent += watchlistTabsStyleString;
    style.textContent += EmptyWatchlistStateString;
    style.textContent += InstrumentSearchStyleString;
    style.textContent += InstrumentTableStyleString;
    this.container = document.createElement("div");
    this.container.id = "preact-root";

    this.shadowRoot!.appendChild(style);
    this.shadowRoot!.appendChild(this.container);
  }

  connectedCallback() {
    if (this.container) {
      render(
        <QueryClientProvider client={queryClient}>
          <Provider value={client}>
            <WatchlistWidget />
          </Provider>
        </QueryClientProvider>,
        this.container
      );
    }
  }

  disconnectedCallback() {
    if (this.container) {
      render(null, this.container);
    }
  }
}

class EurolandAddInstrument extends HTMLElement {
  private container: HTMLDivElement | null = null;

  constructor() {
    super();

    this.attachShadow({ mode: "open" });

    const style = document.createElement("style");
    style.textContent = styleString;
    style.textContent += AddInstrumentStyleString;
    style.textContent += WatchlistModalStyleString;
    this.container = document.createElement("div");
    this.container.id = "preact-root";
    this.shadowRoot!.appendChild(style);
    this.shadowRoot!.appendChild(this.container);
  }

  connectedCallback() {
    if (this.container) {
      render(
        <QueryClientProvider client={queryClient}>
          <Provider value={client}>
            <AddInstrumentWidget
              instrumentId={this.getAttribute("instrumentId") as string}
            />
          </Provider>
        </QueryClientProvider>,
        this.container
      );
    }
  }

  disconnectedCallback() {
    if (this.container) {
      render(null, this.container);
    }
  }
}

class EurolandWatchlistRoot extends HTMLElement {
  private container: HTMLDivElement | null = null;
  connectedCallback() {
    this.attachShadow({ mode: "open" });

    const style = document.createElement("style");
    style.textContent = styleString;
    style.textContent += AddInstrumentStyleString;
    style.textContent += WatchlistModalStyleString;
    this.container = document.createElement("div");
    this.container.id = "preact-root";
    this.shadowRoot!.appendChild(style);
    this.shadowRoot!.appendChild(this.container);

    if (location.pathname !== "/watchlist-add-instrument") return;
    render(<RootIframeWidget />, this.container);
  }

  disconnectedCallback() {
    if (this.container) {
      render(null, this.container);
    }
  }
}

customElements.define("euroland-watch-list", EurolandWatchList);
customElements.define("euroland-add-instrument", EurolandAddInstrument);
customElements.define("euroland-watchlist-root", EurolandWatchlistRoot);
