import { WatchlistModal } from "./addInstrument/WatchlistModal";
import { useState } from "preact/hooks";
import { useWatchlistQueries } from "../services/watchlistQueries";
import { toast } from "react-toastify";
import { useAuth } from "./watchlist/hooks/useAuth";

function RootIframeWidget() {
  const auth = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  // const { watchlistsQuery, addInstrumentMutation, createWatchlistMutation } =
  //   useWatchlistQueries();

  // // Get instrumentId from xprops if available, fallback to prop
  const xpropsInstrumentId = window.xprops?.instrumentId;
  const instrumentId = xpropsInstrumentId || "0";
  console.log({instrumentId});

  // const handleAddToWatchlist = async (watchlistId: string) => {
  //   setIsLoading(true);
  //   try {
  //     await addInstrumentMutation.mutate({
  //       watchlistId,
  //       instrumentId: parseInt(instrumentId),
  //     });

  //     toast.success("Instrument added to watchlist");
  //     watchlistsQuery.refetch();
  //   } catch (error) {
  //     toast.error("Failed to add instrument");
  //     console.error("Error adding instrument to watchlist:", error);
  //   } finally {
  //     setIsLoading(false);
  //   }
  // };

  // const handleCreateWatchlist = async (name: string) => {
  //   setIsLoading(true);
  //   try {
  //     await createWatchlistMutation.mutate(name);
  //     toast.success("Watchlist created successfully");
  //     watchlistsQuery.refetch();
  //   } catch (error) {
  //     toast.error("Failed to create watchlist");
  //     console.error("Error creating new watchlist:", error);
  //   } finally {
  //     setIsLoading(false);
  //   }
  // };

  if (!auth.isAuthenticated) {
    return null;
  }
  return (
    <div className="add-instrument-container">
      <WatchlistModal
        watchlists={[]}
        instrumentId={instrumentId}
        onAddToWatchlist={() => {}}
        onCreateWatchlist={() => {}}
        isLoading={isLoading}
      />
    </div>
  );
}

export default RootIframeWidget;